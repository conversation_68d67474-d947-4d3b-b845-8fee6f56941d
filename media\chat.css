body, html {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    box-sizing: border-box;
}

/* Toolbar styles */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid var(--vscode-side-bar-border);
    background-color: var(--vscode-side-bar-background);
    min-height: 35px;
}

.toolbar-left {
    display: flex;
    align-items: center;
}

.title {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    color: var(--vscode-side-bar-foreground);
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-btn {
    background: none;
    border: 1px solid transparent;
    color: var(--vscode-side-bar-foreground);
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.toolbar-btn:hover {
    background-color: var(--vscode-list-hover-background);
    border-color: var(--vscode-input-border);
}

.menu-btn {
    font-size: 16px;
    font-weight: bold;
    padding: 2px 6px;
}

/* Model tabs styles */
.model-tabs {
    display: flex;
    border-bottom: 1px solid var(--vscode-side-bar-border);
    background-color: var(--vscode-editor-background);
}

.model-tab {
    padding: 8px 16px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    color: var(--vscode-tab-inactive-foreground);
    background-color: var(--vscode-tab-inactive-background);
    border-right: 1px solid var(--vscode-side-bar-border);
    transition: all 0.2s;
}

.model-tab:hover {
    background-color: var(--vscode-tab-hover-background);
}

.model-tab.active {
    color: var(--vscode-tab-active-foreground);
    background-color: var(--vscode-tab-active-background);
    border-bottom-color: var(--vscode-tab-active-border);
}

/* Chat area styles */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-window {
    display: none;
    flex-direction: column;
    height: 100%;
}

.chat-window.active {
    display: flex;
}

.chat-history {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
}

.message {
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 15px;
    max-width: 80%;
    word-wrap: break-word;
}

.user-message {
    background-color: var(--vscode-list-active-selection-background);
    color: var(--vscode-list-active-selection-foreground);
    align-self: flex-end;
    margin-left: auto;
}

.bot-message {
    background-color: var(--vscode-editor-widget-background);
    align-self: flex-start;
}

.error-message {
    background-color: var(--vscode-input-validation-error-background);
    color: var(--vscode-input-validation-error-foreground);
    border: 1px solid var(--vscode-input-validation-error-border);
    align-self: flex-start;
}

.chat-input {
    display: flex;
    padding: 10px;
    border-top: 1px solid var(--vscode-side-bar-border);
}

.chat-input input {
    flex-grow: 1;
    border: 1px solid var(--vscode-input-border);
    border-radius: 5px;
    padding: 8px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
}

.chat-input button {
    margin-left: 10px;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: 1px solid var(--vscode-button-border);
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
}

.chat-input button:hover {
    background-color: var(--vscode-button-hover-background);
}