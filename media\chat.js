// chat.js
console.log('chat.js loaded');
const vscode = acquireVsCodeApi();
const modelTabsContainer = document.getElementById('model-tabs');
const chatArea = document.getElementById('chat-area');

// Track current active model
let activeModel = null;
let availableModels = {};

// Function to create a model tab
function createModelTab(modelKey, modelName) {
  const tab = document.createElement('div');
  tab.className = 'model-tab';
  tab.id = `tab_${modelKey}`;
  tab.textContent = modelName;
  tab.addEventListener('click', () => switchToModel(modelKey));
  modelTabsContainer.appendChild(tab);
}

// Function to create a chat window for a model
function createChatWindow(modelKey, modelName) {
  const chatWindow = document.createElement('div');
  chatWindow.className = 'chat-window';
  chatWindow.id = `chatWindow_${modelKey}`;

  chatWindow.innerHTML = `
        <div class="chat-history" id="chatHistory_${modelKey}"></div>
        <div class="chat-input">
            <input type="text" id="userInput_${modelKey}" placeholder="Type a message...">
            <button id="sendButton_${modelKey}">Send</button>
        </div>
    `;

  chatArea.appendChild(chatWindow);

  // Add event listeners for the new elements
  document.getElementById(`sendButton_${modelKey}`).addEventListener('click', () => sendMessage(modelKey));
  document.getElementById(`userInput_${modelKey}`).addEventListener('keydown', e => {
    if (e.key === 'Enter') {
      sendMessage(modelKey);
    }
  });
}

// Function to switch to a specific model
function switchToModel(modelKey) {
  // Update active model
  activeModel = modelKey;

  // Update tab states
  document.querySelectorAll('.model-tab').forEach(tab => {
    tab.classList.remove('active');
  });
  document.getElementById(`tab_${modelKey}`).classList.add('active');

  // Update chat window visibility
  document.querySelectorAll('.chat-window').forEach(window => {
    window.classList.remove('active');
  });
  document.getElementById(`chatWindow_${modelKey}`).classList.add('active');
}

// Function to add a message to a specific chat window
function addMessage(modelKey, text, className) {
  const chatHistory = document.getElementById(`chatHistory_${modelKey}`);
  if (!chatHistory) return;
  const messageDiv = document.createElement('div');
  messageDiv.className = 'message ' + className;
  messageDiv.textContent = text; // Use textContent for security
  chatHistory.appendChild(messageDiv);
  chatHistory.scrollTop = chatHistory.scrollHeight;
}

// Function to send a message to the extension backend
function sendMessage(modelKey) {
  const userInput = document.getElementById(`userInput_${modelKey}`);
  if (userInput && userInput.value) {
    const text = userInput.value;
    addMessage(modelKey, `You: ${text}`, 'user-message');
    vscode.postMessage({
      type: 'userInput',
      model: modelKey,
      input: text,
    });
    userInput.value = '';
  }
}

// Listen for messages from the extension
window.addEventListener('message', event => {
  const message = event.data; // The message from the extension
  console.log('[chat.js] received message:', message);
  switch (message.type) {
    case 'initialize':
      console.log('[chat.js] received initialize:', message);
      // Store available models
      availableModels = message.models || {};

      // Clear existing tabs and windows
      modelTabsContainer.innerHTML = '';
      chatArea.innerHTML = '';

      // Create tabs and windows for each model
      let firstModel = null;
      for (const modelKey in availableModels) {
        if (!firstModel) firstModel = modelKey;
        createModelTab(modelKey, availableModels[modelKey].name);
        createChatWindow(modelKey, availableModels[modelKey].name);
      }

      // Activate the first model by default
      if (firstModel) {
        switchToModel(firstModel);
      }
      break;
    case 'update':
      // Add a bot message to the correct window
      if (message.model && message.data) {
        addMessage(message.model, `Bot: ${message.data}`, 'bot-message');
      }
      break;
    case 'addResponse':
      // Add a bot response to all chat windows (since we don't know which model it's for)
      if (message.content) {
        // Get all model keys from existing chat windows
        const chatWindows = document.querySelectorAll('.chat-window');
        chatWindows.forEach(window => {
          const modelKey = window.id.replace('chatWindow_', '');
          addMessage(modelKey, `Bot: ${message.content}`, 'bot-message');
        });
      }
      break;
    case 'showError':
      // Show error message in all chat windows
      if (message.error) {
        const chatWindows = document.querySelectorAll('.chat-window');
        chatWindows.forEach(window => {
          const modelKey = window.id.replace('chatWindow_', '');
          addMessage(modelKey, `Error: ${message.error}`, 'error-message');
        });
      }
      break;
    // Add other cases as needed, e.g., for history
  }
});

// Add toolbar button event listeners
document.getElementById('addConfigBtn').addEventListener('click', () => {
  vscode.postMessage({ type: 'addConfig' });
});

document.getElementById('settingsBtn').addEventListener('click', () => {
  vscode.postMessage({ type: 'openSettings' });
});

document.getElementById('menuBtn').addEventListener('click', () => {
  vscode.postMessage({ type: 'showMenu' });
});

// Tell the extension that the webview is ready to be initialized
vscode.postMessage({ type: 'webviewReady' });
