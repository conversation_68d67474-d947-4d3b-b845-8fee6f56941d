import * as vscode from 'vscode';

import type { RequestEntryPoint } from '../entry/RequestEntryPoint.js';
import type { ModelConfig } from '../subject/modelConfigs.js';
import type { MessageFromWebview, MessageToWebview } from './webviewMessageProtocol.js';
import { message_from_webview_schema } from './webviewMessageProtocol.js';

/**
 * A type guard to check if a value is an instance of Map.
 * @param value The value to check.
 * @returns True if the value is a Map, otherwise false.
 */
function is_map(value: unknown): value is Map<unknown, unknown> {
  return value instanceof Map;
}

/**
 * @description Provides the webview panel for the chat interface.
 * This class is responsible for creating the webview, loading the static HTML,
 * and acting as a message broker between the webview and the extension's backend.
 */
export class ChatPanelProvider implements vscode.WebviewViewProvider {
  public static readonly view_type = 'llm-bridge.chat';
  private _view?: vscode.WebviewView;
  private readonly _context: vscode.ExtensionContext;
  private readonly _request_entry_point: RequestEntryPoint;
  private readonly _model_configs: Map<string, ModelConfig>;
  private readonly _model_key: string; // 每个Provider实例对应一个特定的模型
  private _chat_history: Array<{type: 'user' | 'bot' | 'error', content: string, timestamp: number}> = [];
  private _current_input: string = '';

  constructor(
    context: vscode.ExtensionContext,
    request_entry_point: RequestEntryPoint,
    model_configs: Map<string, ModelConfig>,
    model_key: string,
  ) {
    this._context = context;
    this._request_entry_point = request_entry_point;
    // The linter flags an unsafe assignment, likely due to type resolution issues
    // originating from other files. We disable the rule here because the property's
    // usage is protected by a type guard to ensure runtime safety.
    this._model_configs = model_configs;
    this._model_key = model_key;
  }

  // This method must be camelCase to correctly implement the vscode.WebviewViewProvider interface.
  // eslint-disable-next-line @typescript-eslint/naming-convention
  public async resolveWebviewView(
    webview_view: vscode.WebviewView,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _context: vscode.WebviewViewResolveContext,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _token: vscode.CancellationToken,
  ): Promise<void> {
    this._view = webview_view;

    // 使用构造函数中指定的模型键
    const current_model_key = this._model_key;
    const view_type = webview_view.viewType;

    webview_view.webview.options = {
      enableScripts: true,
      localResourceRoots: [vscode.Uri.joinPath(this._context.extensionUri, 'media')],
    };

    // 为每个模型生成独立的HTML内容
    webview_view.webview.html = this._generate_independent_html(webview_view.webview);

    webview_view.webview.onDidReceiveMessage(async (message: unknown) => {
      const parsed_message = message_from_webview_schema.safeParse(message);

      if (!parsed_message.success) {
        console.error('Received invalid message from webview:', parsed_message.error);
        return;
      }

      const safe_message: MessageFromWebview = parsed_message.data;

      try {
        switch (safe_message.type) {
          case 'userInput':
            await this._request_entry_point.handle_user_request(safe_message.input, safe_message.model);
            break;
          case 'webviewReady': {
            if (is_map(this._model_configs) && current_model_key && this._model_configs.has(current_model_key)) {
              const model_config = this._model_configs.get(current_model_key)!;
              const models = { [current_model_key]: { name: model_config.name } };
              console.log(`[ChatPanelProvider] webviewReady for ${view_type}, model:`, JSON.stringify(models));
              this.post_message({
                type: 'initialize',
                models,
              });
              this.post_message({
                type: 'updateModelList',
                models: [current_model_key],
              });
            } else {
              console.log(`[ChatPanelProvider] No model found for viewType: ${view_type}`);
              this.post_message({ type: 'updateModelList', models: [] });
            }
            break;
          }
          case 'addConfig':
            void vscode.window.showInformationMessage('添加配置功能将在后续版本中实现');
            break;
          case 'openSettings':
            void vscode.window.showInformationMessage('设置功能将在后续版本中实现');
            break;
          case 'showMenu':
            void vscode.window.showInformationMessage('菜单功能将在后续版本中实现');
            break;
        }
      } catch (error: unknown) {
        const error_message = error instanceof Error ? error.message : String(error);
        void vscode.window.showErrorMessage(`Error handling webview message: ${error_message}`);
      }
    });
  }

  private _update_resource_uris(html_content: string, webview: vscode.Webview): string {
    return html_content
      .replace(/<link rel="stylesheet" href="(.*?)">/g, (_match: string, href: string) => {
        const resource_uri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'media', href));
        return `<link rel="stylesheet" href="${resource_uri.toString(true)}">`;
      })
      .replace(/<script src="(.*?)"><\/script>/g, (_match: string, src: string) => {
        const resource_uri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'media', src));
        return `<script src="${resource_uri.toString(true)}"></script>`;
      });
  }

  public post_message(message: MessageToWebview): void {
    console.log('[ChatPanelProvider] post_message:', JSON.stringify(message));
    if (this._view) {
      void this._view.webview.postMessage(message);
    }
  }
}
