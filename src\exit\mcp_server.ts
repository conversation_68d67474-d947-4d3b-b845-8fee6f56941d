import express, { type Express, type Request, type Response } from 'express';
import type { Server } from 'http';
import { z } from 'zod';

import type { Tool } from '../verb/moduleInterfaces.js';

const execute_tool_request_schema = z.object({
  tool_name: z.string(),
  parameters: z.record(z.unknown()),
});

export class McpServer {
  private readonly _app: Express;
  private _server: Server | null = null;
  private readonly _tools: Record<string, Tool>;

  constructor(tools: Record<string, Tool>) {
    this._app = express();
    this._app.use(express.json());
    this._tools = tools;
    this._setup_routes();
  }

  private _setup_routes(): void {
    this._app.post('/execute_tool', async (req: Request, res: Response): Promise<void> => {
      try {
        const validation_result = execute_tool_request_schema.safeParse(req.body);
        if (!validation_result.success) {
          res.status(400).json({ error: 'Invalid request body', details: validation_result.error.issues });
          return;
        }

        const { tool_name, parameters } = validation_result.data;
        const tool = this._tools[tool_name];

        if (!tool) {
          res.status(404).json({ error: `Tool '${tool_name}' not found.` });
          return;
        }

        const result: unknown = await tool.handler(parameters);
        res.status(200).json({ result });
      } catch (error: unknown) {
        const error_message = error instanceof Error ? error.message : 'An unknown error occurred';
        const tool_name_for_error = execute_tool_request_schema.safeParse(req.body).success
          ? execute_tool_request_schema.parse(req.body).tool_name
          : 'unknown';
        res.status(500).json({ error: `Error executing tool '${tool_name_for_error}': ${error_message}` });
      }
    });
  }

  public start(port: number): void {
    this._server = this._app.listen(port, () => {
      console.log(`MCP server listening on port ${port}`);
    });
  }

  public stop(): void {
    if (this._server) {
      this._server.close(() => {
        console.log('MCP server stopped');
      });
    }
  }
}
