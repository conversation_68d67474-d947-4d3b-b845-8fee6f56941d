import { z } from 'zod';

// ====================================================================================
// Messages SENT FROM the Webview TO the Extension
// ====================================================================================

const user_input_message_schema = z.object({
  type: z.literal('userInput'),
  input: z.string(),
  model: z.string(),
});

const webview_ready_message_schema = z.object({
  type: z.literal('webviewReady'),
});

const add_config_message_schema = z.object({
  type: z.literal('addConfig'),
});

const open_settings_message_schema = z.object({
  type: z.literal('openSettings'),
});

const show_menu_message_schema = z.object({
  type: z.literal('showMenu'),
});

/**
 * @description A discriminated union of all possible messages that can be sent FROM the webview TO the extension.
 * This schema is used to validate incoming messages in the extension's backend.
 */
export const message_from_webview_schema = z.discriminatedUnion('type', [
  user_input_message_schema,
  webview_ready_message_schema,
  add_config_message_schema,
  open_settings_message_schema,
  show_menu_message_schema,
]);

/**
 * @description The TypeScript type inferred from the Zod schema for messages sent FROM the webview.
 */
export type MessageFromWebview = z.infer<typeof message_from_webview_schema>;

// ====================================================================================
// Messages SENT FROM the Extension TO the Webview
// ====================================================================================

const add_response_message_schema = z.object({
  type: z.literal('addResponse'),
  content: z.string(),
});

const show_error_message_schema = z.object({
  type: z.literal('showError'),
  error: z.string(),
});

const update_model_list_message_schema = z.object({
  type: z.literal('updateModelList'),
  models: z.array(z.string()),
});

const initialize_message_schema = z.object({
  type: z.literal('initialize'),
  models: z.record(z.string(), z.object({ name: z.string() })),
});

const update_message_schema = z.object({
  type: z.literal('update'),
  model: z.string(),
  data: z.string(),
});

/**
 * @description A discriminated union of all possible messages that can be sent FROM the extension TO the webview.
 * This is used to type the `postMessage` function in the extension's backend.
 */
export const message_to_webview_schema = z.discriminatedUnion('type', [
  add_response_message_schema,
  show_error_message_schema,
  update_model_list_message_schema,
  initialize_message_schema,
  update_message_schema,
]);
/**
 * @description The TypeScript type inferred from the Zod schema for messages sent TO the webview.
 */
export type MessageToWebview = z.infer<typeof message_to_webview_schema>;
