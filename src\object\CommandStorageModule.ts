import * as fs from 'fs';
import * as path from 'path';
import type * as vscode from 'vscode';
import { z } from 'zod';

// Zod schemas for validation
export const command_schema = z.object({
  id: z.string(),
  command: z.string(),
  is_builtin: z.boolean(),
});
export type Command = z.infer<typeof command_schema>;
const command_list_schema = z.array(command_schema);
const whitelist_schema = z.array(z.string());

const DEFAULT_WHITELIST = [
  'workbench.action.quickOpen',
  'workbench.action.showCommands',
  'workbench.action.tasks.runTask',
  'workbench.action.debug.start',
  'workbench.action.files.saveAll',
];

export class CommandStorageModule {
  private _all_commands: Command[] = [];
  private _whitelist: string[] = [];
  private readonly _commands_file_path: string;
  private readonly _whitelist_file_path: string;

  constructor(private readonly _context: vscode.ExtensionContext) {
    const storage_path = this._context.globalStorageUri.fsPath;
    if (!fs.existsSync(storage_path)) {
      fs.mkdirSync(storage_path, { recursive: true });
    }
    this._commands_file_path = path.join(storage_path, 'all_commands.json');
    this._whitelist_file_path = path.join(storage_path, 'whitelist.json');

    this._load_all_commands();
    this._load_whitelist();
  }

  private _load_all_commands(): void {
    try {
      if (fs.existsSync(this._commands_file_path)) {
        const file_content = fs.readFileSync(this._commands_file_path, 'utf-8');
        const parsed_json: unknown = JSON.parse(file_content);
        const result = command_list_schema.safeParse(parsed_json);
        if (result.success) {
          this._all_commands = result.data;
        } else {
          this._all_commands = [];
        }
      }
    } catch (error) {
      this._all_commands = [];
      console.error('Error loading commands:', error);
    }
  }

  private _load_whitelist(): void {
    try {
      if (fs.existsSync(this._whitelist_file_path)) {
        const file_content = fs.readFileSync(this._whitelist_file_path, 'utf-8');
        const parsed_json: unknown = JSON.parse(file_content);
        const result = whitelist_schema.safeParse(parsed_json);
        if (result.success) {
          this._whitelist = result.data;
        } else {
          this._reset_whitelist_sync();
        }
      } else {
        this._reset_whitelist_sync();
      }
    } catch (error) {
      this._reset_whitelist_sync();
      console.error('Error loading whitelist:', error);
    }
  }

  private _reset_whitelist_sync(): void {
    this._whitelist = DEFAULT_WHITELIST;
    this._save_whitelist_sync();
  }

  private _save_whitelist_sync(): void {
    try {
      fs.writeFileSync(this._whitelist_file_path, JSON.stringify(this._whitelist, null, 2));
    } catch (error) {
      console.error('Error saving whitelist:', error);
    }
  }

  public get_all_commands(): Command[] {
    return this._all_commands;
  }

  public get_whitelist_ids(): string[] {
    return this._whitelist;
  }

  public async save_commands(commands: Command[]): Promise<void> {
    const result = command_list_schema.safeParse(commands);
    if (result.success) {
      this._all_commands = result.data;
      await fs.promises.writeFile(this._commands_file_path, JSON.stringify(this._all_commands, null, 2));
    }
  }

  public async save_whitelist(whitelist: string[]): Promise<void> {
    const result = whitelist_schema.safeParse(whitelist);
    if (result.success) {
      this._whitelist = result.data;
      await fs.promises.writeFile(this._whitelist_file_path, JSON.stringify(this._whitelist, null, 2));
    }
  }

  public async reset_whitelist(): Promise<void> {
    this._whitelist = DEFAULT_WHITELIST;
    await this.save_whitelist(this._whitelist);
  }
}
