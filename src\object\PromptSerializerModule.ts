import type { IComposableModule } from '../verb/moduleInterfaces.js';
import type { ContextPackage, ExecutionResult, LlmTool } from './dataModels.js';

/**
 * @description Serializes the context package into a natural language prompt for the LLM.
 * This module is composable and returns the modified context package.
 */
export class PromptSerializerModule implements IComposableModule {
  public execute(context: ContextPackage): Promise<ContextPackage> {
    // 使用配置中的提示词模板，如果没有则使用默认模板
    const templates = context.model_config?.prompt_templates;

    let prompt = '';

    // 系统提示词
    if (templates?.system_prompt) {
      prompt += templates.system_prompt + '\n\n';
    } else {
      prompt += `You are a helpful VS Code assistant. Your goal is to help the user with their request by using the available tools.\n\n`;
    }

    // 用户请求和环境上下文
    if (templates?.user_prompt_template) {
      prompt += templates.user_prompt_template
        .replace('{{user_request}}', context.user_request)
        .replace('{{workspace_root}}', context.workspace_root || 'None')
        .replace('{{active_file_path}}', context.active_file_path || 'None')
        .replace('{{selected_text}}', context.selected_text || 'None');
    } else {
      prompt += `## User Request
${context.user_request}

## Environment Context
- Workspace Root: ${context.workspace_root}
- Active File: ${context.active_file_path}
- Selected Text: ${context.selected_text || 'None'}
`;
    }

    // Handle the history of tool executions
    if (context.tool_executions && context.tool_executions.length > 0) {
      prompt += '\n## Previous Actions\n';
      prompt += 'You have already taken the following actions:\n';
      context.tool_executions.forEach((exec: ExecutionResult, index: number) => {
        prompt += `Step ${index + 1}: You used the tool \`${exec.command.tool_name}\` with parameters ${JSON.stringify(exec.command.parameters)}.\n`;
        if (exec.is_success) {
          // If the output is an array (like from searchCommands), format it as a list.
          const output = Array.isArray(exec.output) ? exec.output.join(', ') : String(exec.output);
          prompt += `Result: The tool returned the following: ${output}\n`;
        } else {
          prompt += `Error: The tool failed with the following error: ${exec.error}\n`;
        }
      });
      prompt += '\nBased on the results of your previous actions, decide on the next step.\n';
    }

    prompt += '\n## Available Tools\n';
    prompt +=
      'You can use the following tools. Respond with a single JSON object in the format: { "tool": "<tool_name>", "parameters": { "<param_name>": "<value>" } }\n';

    // Dynamically add executeCommand tools if the last action was a successful search
    if (context.tool_executions && context.tool_executions.length > 0) {
      const last_execution = context.tool_executions[context.tool_executions.length - 1];
      if (
        last_execution &&
        last_execution.is_success &&
        last_execution.command.tool_name === 'searchCommands' &&
        Array.isArray(last_execution.output)
      ) {
        const command_ids = last_execution.output as string[];
        if (command_ids.length > 0) {
          const execute_command_tool: LlmTool = {
            name: 'vscode.executeCommand',
            description: 'Executes one of the previously found VS Code commands.',
            parameters: {
              type: 'object',
              properties: {
                command_id: {
                  type: 'string',
                  description: 'The ID of the command to execute.',
                  enum: command_ids, // Provide the found commands as an enum
                },
                args: {
                  type: 'array',
                  description: 'Optional arguments for the command.',
                },
              },
              required: ['command_id'],
            },
          };
          prompt += this.format_tool(execute_command_tool);
        } else {
          prompt +=
            '\n- No relevant commands were found by your search. Try searching with a different query or reply to the user for more information.\n';
        }
      }
    }

    // Always include the static tools
    if (context.available_tools) {
      context.available_tools.forEach((tool: LlmTool) => {
        prompt += this.format_tool(tool);
      });
    }

    prompt += '\nPlease provide the JSON command to execute.';

    // Attach the generated prompt to the context and return the whole package
    context.prompt_string = prompt;
    return Promise.resolve(context);
  }

  private format_tool(tool: LlmTool): string {
    return `
### ${tool.name}
- Description: ${tool.description}
- Parameters: ${JSON.stringify(tool.parameters, null, 2)}
`;
  }
}
