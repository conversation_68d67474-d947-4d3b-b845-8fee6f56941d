import axios from 'axios';
import * as vscode from 'vscode';
import { z } from 'zod';
import * as crypto from 'crypto';
import WebSocket from 'ws';

import type { ContextPackage, ExecutionResult, LlmCommand } from '../object/dataModels.js';
import type { IComposableModule } from '../verb/moduleInterfaces.js';
import type { MistralConfig, SparkConfig, ModelConfig } from './modelConfigs.js';

// Zod schema for validating the structure of an LLM command
const llm_command_schema = z.object({
  tool_name: z.string(),
  parameters: z.record(z.union([z.string(), z.number(), z.boolean(), z.array(z.string())])),
});

// Interfaces for the external LLM API response
interface Message {
  role: string;
  content: string;
}

interface Choice {
  message: Message;
}

interface LlmApiResponse {
  choices: Choice[];
}

// Interface for the expected structure of an Axios error response from the LLM API
interface LlmApiErrorData {
  error?: {
    message?: string;
  };
}

function is_mistral_config(config: ModelConfig): config is MistralConfig {
  return config.api_type === 'mistral';
}

function is_spark_config(config: ModelConfig): config is SparkConfig {
  return config.api_type === 'spark';
}

export class ExternalLlmClientModule implements IComposableModule {
  constructor(private _all_model_configs: Map<string, ModelConfig>) {}

  public async execute(context: ContextPackage): Promise<ContextPackage> {
    // Correctly destructure snake_case property
    const { model_key, prompt_string } = context;

    console.log(`🚀 [ExternalLlmClientModule] 开始执行 - 模型: ${model_key}`);
    console.log(`📝 [ExternalLlmClientModule] 发送的提示词:\n${prompt_string}`);

    if (!model_key) {
      console.error('❌ [ExternalLlmClientModule] Model key is missing from the context package.');
      void vscode.window.showErrorMessage('Model key is missing from the context package.');
      return context;
    }

    const model_config = this._all_model_configs.get(model_key);
    if (!model_config) {
      console.error(`❌ [ExternalLlmClientModule] Configuration for model '${model_key}' not found.`);
      void vscode.window.showErrorMessage(`Configuration for model '${model_key}' not found.`);
      return context;
    }

    // Check if the model config is supported
    if (!is_mistral_config(model_config) && !is_spark_config(model_config)) {
      console.error(`❌ [ExternalLlmClientModule] Model '${model_key}' is not a supported model type.`);
      void vscode.window.showErrorMessage(`Model '${model_key}' is not a supported model type.`);
      return context;
    }

    if (!prompt_string) {
      console.error('❌ [ExternalLlmClientModule] Prompt is missing from the context package.');
      void vscode.window.showErrorMessage('Prompt is missing from the context package.');
      return context;
    }

    try {
      let response_content: string | undefined;

      if (is_mistral_config(model_config)) {
        console.log(`🌐 [ExternalLlmClientModule] 调用 Mistral API: ${model_config.api_config.base_url}`);

        // Handle Mistral API
        const request_payload = {
          model: model_config.api_config.model,
          messages: [{ role: 'user', content: prompt_string }],
        };

        console.log(`📤 [ExternalLlmClientModule] Mistral 请求载荷:`, JSON.stringify(request_payload, null, 2));

        const response = await axios.post<LlmApiResponse>(
          model_config.api_config.base_url,
          request_payload,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${model_config.api_config.api_key}`,
            },
          },
        );

        console.log(`📥 [ExternalLlmClientModule] Mistral 原始响应:`, JSON.stringify(response.data, null, 2));
        response_content = response.data.choices?.[0]?.message?.content;

      } else if (is_spark_config(model_config)) {
        console.log(`🌐 [ExternalLlmClientModule] 调用 Spark WebSocket API: ${model_config.api_config.ws_url}`);

        // Handle Spark API with real WebSocket implementation
        response_content = await this._call_spark_api(model_config, prompt_string);
      }

      if (response_content) {
        console.log(`📥 [ExternalLlmClientModule] LLM 原始响应内容:\n${response_content}`);

        try {
          // 清理响应内容，移除可能的markdown代码块标记
          let cleaned_content = response_content.trim();
          console.log(`🧹 [ExternalLlmClientModule] 清理前的响应:\n${cleaned_content}`);

          // 移除markdown代码块标记
          if (cleaned_content.startsWith('```json')) {
            cleaned_content = cleaned_content.replace(/^```json\s*/, '').replace(/\s*```$/, '');
            console.log(`🧹 [ExternalLlmClientModule] 移除 ```json 标记后:\n${cleaned_content}`);
          } else if (cleaned_content.startsWith('```')) {
            cleaned_content = cleaned_content.replace(/^```\s*/, '').replace(/\s*```$/, '');
            console.log(`🧹 [ExternalLlmClientModule] 移除 ``` 标记后:\n${cleaned_content}`);
          }

          console.log(`🔍 [ExternalLlmClientModule] 尝试解析 JSON:\n${cleaned_content}`);
          const command_json: unknown = JSON.parse(cleaned_content);
          console.log(`✅ [ExternalLlmClientModule] JSON 解析成功:`, JSON.stringify(command_json, null, 2));

          const parsed_command = llm_command_schema.safeParse(command_json);
          if (parsed_command.success) {
            console.log(`✅ [ExternalLlmClientModule] 命令验证成功:`, JSON.stringify(parsed_command.data, null, 2));
            const new_execution: ExecutionResult = {
              is_success: false, // It's not successful until the tool runs
              command: parsed_command.data as LlmCommand,
              output: null, // No output yet
            };
            context.tool_executions = [...(context.tool_executions || []), new_execution];
            console.log(`📋 [ExternalLlmClientModule] 添加到执行队列，当前队列长度: ${context.tool_executions.length}`);
          } else {
            console.error(`❌ [ExternalLlmClientModule] 命令格式验证失败:`, parsed_command.error.message);
            void vscode.window.showErrorMessage(
              `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
            );
          }
        } catch (e: unknown) {
          const error_message = e instanceof Error ? e.message : 'An unknown error occurred during parsing.';
          console.error(`❌ [ExternalLlmClientModule] JSON 解析失败: ${error_message}`);
          console.error(`❌ [ExternalLlmClientModule] 响应内容预览: ${response_content.substring(0, 200)}...`);
          void vscode.window.showErrorMessage(`Failed to parse LLM response JSON. ${error_message}\nResponse content: ${response_content.substring(0, 200)}...`);
        }
      } else {
        console.error(`❌ [ExternalLlmClientModule] 收到空的或无效的 LLM 响应`);
        void vscode.window.showErrorMessage('Received an empty or invalid response from the LLM.');
      }
    } catch (error: unknown) {
      let message = 'An unknown error occurred while communicating with the LLM.';
      if (axios.isAxiosError(error) && error.response) {
        // Use the defined interface for type safety
        const error_data = error.response.data as LlmApiErrorData;
        message = error_data.error?.message ?? error.message;
      } else if (error instanceof Error) {
        message = error.message;
      }
      void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
    }
    return context;
  }

  /**
   * 调用科大讯飞星火 WebSocket API
   */
  private async _call_spark_api(model_config: SparkConfig, prompt: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const { ws_url, api_key, api_secret, appid, domain } = model_config.api_config;

      // 生成认证URL
      const auth_url = this._generate_spark_auth_url(ws_url, api_key, api_secret);
      console.log(`🔐 [Spark API] 认证URL: ${auth_url}`);

      // 创建WebSocket连接
      const ws = new WebSocket(auth_url);
      let response_text = '';

      ws.on('open', () => {
        console.log(`🔗 [Spark API] WebSocket 连接已建立`);

        // 构建请求数据
        const request_data = {
          header: {
            app_id: appid,
            uid: 'user_' + Date.now(),
          },
          parameter: {
            chat: {
              domain: domain,
              temperature: 0.5,
              max_tokens: 2048,
            },
          },
          payload: {
            message: {
              text: [
                {
                  role: 'user',
                  content: prompt,
                },
              ],
            },
          },
        };

        console.log(`📤 [Spark API] 发送请求数据:`, JSON.stringify(request_data, null, 2));
        ws.send(JSON.stringify(request_data));
      });

      ws.on('message', (data) => {
        try {
          const response = JSON.parse(data.toString());
          console.log(`📥 [Spark API] 收到响应:`, JSON.stringify(response, null, 2));

          if (response.header?.code !== 0) {
            console.error(`❌ [Spark API] 错误响应:`, response.header);
            reject(new Error(`Spark API Error: ${response.header?.message || 'Unknown error'}`));
            return;
          }

          // 累积响应文本
          if (response.payload?.choices?.text) {
            for (const choice of response.payload.choices.text) {
              response_text += choice.content || '';
            }
          }

          // 检查是否完成
          if (response.header?.status === 2) {
            console.log(`✅ [Spark API] 响应完成，最终文本:\n${response_text}`);
            ws.close();
            resolve(response_text);
          }
        } catch (error) {
          console.error(`❌ [Spark API] 解析响应失败:`, error);
          reject(error);
        }
      });

      ws.on('error', (error) => {
        console.error(`❌ [Spark API] WebSocket 错误:`, error);
        reject(error);
      });

      ws.on('close', () => {
        console.log(`🔌 [Spark API] WebSocket 连接已关闭`);
        if (response_text) {
          resolve(response_text);
        } else {
          reject(new Error('WebSocket closed without response'));
        }
      });

      // 设置超时
      setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close();
          reject(new Error('Spark API timeout'));
        }
      }, model_config.api_config.timeout || 30000);
    });
  }

  /**
   * 生成科大讯飞星火API认证URL
   */
  private _generate_spark_auth_url(ws_url: string, api_key: string, api_secret: string): string {
    const url = new URL(ws_url);
    const host = url.host;
    const path = url.pathname;
    const date = new Date().toUTCString();

    // 构建签名字符串
    const signature_origin = `host: ${host}\ndate: ${date}\nGET ${path} HTTP/1.1`;
    const signature_sha = crypto.createHmac('sha256', api_secret).update(signature_origin).digest('base64');
    const authorization_origin = `api_key="${api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="${signature_sha}"`;
    const authorization = Buffer.from(authorization_origin).toString('base64');

    // 构建认证URL
    const auth_url = `${ws_url}?authorization=${encodeURIComponent(authorization)}&date=${encodeURIComponent(date)}&host=${encodeURIComponent(host)}`;

    return auth_url;
  }
}
