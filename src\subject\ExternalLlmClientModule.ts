import axios from 'axios';
import * as vscode from 'vscode';
import { z } from 'zod';

import type { ContextPackage, ExecutionResult, LlmCommand } from '../object/dataModels.js';
import type { IComposableModule } from '../verb/moduleInterfaces.js';
import type { MistralConfig, SparkConfig, ModelConfig } from './modelConfigs.js';

// Zod schema for validating the structure of an LLM command
const llm_command_schema = z.object({
  tool_name: z.string(),
  parameters: z.record(z.union([z.string(), z.number(), z.boolean(), z.array(z.string())])),
});

// Interfaces for the external LLM API response
interface Message {
  role: string;
  content: string;
}

interface Choice {
  message: Message;
}

interface LlmApiResponse {
  choices: Choice[];
}

// Interface for the expected structure of an Axios error response from the LLM API
interface LlmApiErrorData {
  error?: {
    message?: string;
  };
}

function is_mistral_config(config: ModelConfig): config is MistralConfig {
  return config.api_type === 'mistral';
}

function is_spark_config(config: ModelConfig): config is SparkConfig {
  return config.api_type === 'spark';
}

export class ExternalLlmClientModule implements IComposableModule {
  constructor(private _all_model_configs: Map<string, ModelConfig>) {}

  public async execute(context: ContextPackage): Promise<ContextPackage> {
    // Correctly destructure snake_case property
    const { model_key, prompt_string } = context;

    if (!model_key) {
      void vscode.window.showErrorMessage('Model key is missing from the context package.');
      return context;
    }

    const model_config = this._all_model_configs.get(model_key);
    if (!model_config) {
      void vscode.window.showErrorMessage(`Configuration for model '${model_key}' not found.`);
      return context;
    }

    // Check if the model config is supported
    if (!is_mistral_config(model_config) && !is_spark_config(model_config)) {
      void vscode.window.showErrorMessage(`Model '${model_key}' is not a supported model type.`);
      return context;
    }

    if (!prompt_string) {
      void vscode.window.showErrorMessage('Prompt is missing from the context package.');
      return context;
    }

    try {
      let response_content: string | undefined;

      if (is_mistral_config(model_config)) {
        // Handle Mistral API
        const response = await axios.post<LlmApiResponse>(
          model_config.base_url,
          {
            model: model_config.model,
            messages: [{ role: 'user', content: prompt_string }],
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${model_config.api_key}`,
            },
          },
        );
        response_content = response.data.choices?.[0]?.message?.content;
      } else if (is_spark_config(model_config)) {
        // Handle Spark API (simplified HTTP version for now)
        // Note: Spark typically uses WebSocket, but for simplicity we'll use a mock response
        // In a real implementation, you would need to implement WebSocket communication
        void vscode.window.showWarningMessage('Spark API integration is not fully implemented yet. Using mock response.');
        response_content = JSON.stringify({
          tool_name: 'reply_to_user',
          parameters: { message: 'Spark API 模拟响应: ' + prompt_string }
        });
      }

      if (response_content) {
        try {
          const command_json: unknown = JSON.parse(response_content);
          const parsed_command = llm_command_schema.safeParse(command_json);
          if (parsed_command.success) {
            const new_execution: ExecutionResult = {
              is_success: false, // It's not successful until the tool runs
              command: parsed_command.data as LlmCommand,
              output: null, // No output yet
            };
            context.tool_executions = [...(context.tool_executions || []), new_execution];
          } else {
            void vscode.window.showErrorMessage(
              `Received an invalid command format from the LLM: ${parsed_command.error.message}`,
            );
          }
        } catch (e: unknown) {
          const error_message = e instanceof Error ? e.message : 'An unknown error occurred during parsing.';
          void vscode.window.showErrorMessage(`Failed to parse LLM response JSON. ${error_message}`);
        }
      } else {
        void vscode.window.showErrorMessage('Received an empty or invalid response from the LLM.');
      }
    } catch (error: unknown) {
      let message = 'An unknown error occurred while communicating with the LLM.';
      if (axios.isAxiosError(error) && error.response) {
        // Use the defined interface for type safety
        const error_data = error.response.data as LlmApiErrorData;
        message = error_data.error?.message ?? error.message;
      } else if (error instanceof Error) {
        message = error.message;
      }
      void vscode.window.showErrorMessage(`Failed to communicate with LLM: ${message}`);
    }
    return context;
  }
}
