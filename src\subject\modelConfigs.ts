import * as fs from 'fs';
import * as path from 'path';
import { z } from 'zod';

// ====================================================================================
// Zod Schemas for Type-Safe Configuration
// ====================================================================================

const base_model_config_schema = z.object({
  id: z.string(),
  name: z.string(),
});

const mistral_config_schema = base_model_config_schema.extend({
  api_type: z.literal('mistral'),
  api_key: z.string(),
  base_url: z.string(),
  model: z.string(),
});

const spark_config_schema = base_model_config_schema.extend({
  api_type: z.literal('spark'),
  ws_url: z.string(),
  api_secret: z.string(),
  api_key: z.string(),
  appid: z.string(),
  domain: z.string(),
});

const openai_config_schema = base_model_config_schema.extend({
  api_type: z.literal('openai'),
  api_key: z.string(),
  base_url: z.string(),
  model: z.string(),
  organization: z.string().optional(),
});

export const model_config_schema = z.discriminatedUnion('api_type', [
  mistral_config_schema,
  spark_config_schema,
  openai_config_schema,
]);

// ====================================================================================
// Type Definitions Inferred from Zod Schemas
// ====================================================================================

export type MistralConfig = z.infer<typeof mistral_config_schema>;
export type SparkConfig = z.infer<typeof spark_config_schema>;
export type OpenAIConfig = z.infer<typeof openai_config_schema>;
export type ModelConfig = z.infer<typeof model_config_schema>;

// ====================================================================================
// Configuration Loading and Exporting
// ====================================================================================

function load_json_config(file_path: string): unknown {
  const full_path = path.join(__dirname, file_path);
  const file_contents = fs.readFileSync(full_path, 'utf-8');
  return JSON.parse(file_contents);
}

function load_model_configs(): ModelConfig[] {
  // 动态扫描config目录下的所有配置文件
  const config_dir = path.join(__dirname, '../config');
  let config_files: string[] = [];

  try {
    const files = fs.readdirSync(config_dir);
    console.log('🔍 扫描到的配置文件:', files);
    config_files = files
      .filter(file => file.endsWith('_config.json'))
      .map(file => `../config/${file}`);
    console.log('🔍 过滤后的配置文件:', config_files);
  } catch (error) {
    console.warn('无法读取config目录，使用默认配置文件列表:', error);
    // 回退到硬编码的配置文件列表
    config_files = [
      '../config/mistral_config.json',
      '../config/xfyun_spark_config.json',
    ];
  }
  const all_raw_configs: unknown[] = [];
  for (const file of config_files) {
    const raw = load_json_config(file);
    if (Array.isArray(raw)) {
      for (const item of raw) {
        all_raw_configs.push(item);
      }
    } else if (typeof raw === 'object' && raw !== null) {
      all_raw_configs.push(raw);
    }
  }
  // 统一基础schema校验
  const base_schema = z.object({ id: z.string(), name: z.string(), api_type: z.string() });
  for (const model_config_raw of all_raw_configs) {
    if (typeof model_config_raw !== 'object' || model_config_raw === null) {
      throw new Error(`模型配置不是对象: ${JSON.stringify(model_config_raw)}`);
    }
    try {
      base_schema.parse(model_config_raw);
    } catch (e) {
      const error_message = e instanceof Error ? e.message : String(e);
      throw new Error(`模型配置缺少基础字段: ${JSON.stringify(model_config_raw)}\n错误: ${error_message}`);
    }
  }
  // 按api_type分发到专用schema
  const configs: ModelConfig[] = [];
  for (const model_config_raw of all_raw_configs) {
    if (typeof model_config_raw !== 'object' || model_config_raw === null) {
      continue;
    }
    try {
      const cfg = model_config_raw as { api_type: string; id?: string; name?: string };
      switch (cfg.api_type) {
        case 'mistral':
          configs.push(mistral_config_schema.parse(model_config_raw));
          break;
        case 'spark':
          configs.push(spark_config_schema.parse(model_config_raw));
          break;
        case 'openai':
          configs.push(openai_config_schema.parse(model_config_raw));
          break;
        default:
          throw new Error(`未知api_type: ${String(cfg.api_type)}`);
      }
    } catch (e) {
      const error_message = e instanceof Error ? e.message : String(e);
      const cfg = model_config_raw as { id?: string; name?: string; api_type?: string };
      throw new Error(
        `模型(id=${String(cfg.id)}, name=${String(cfg.name)}, api_type=${String(cfg.api_type)})配置校验失败: ${error_message}`,
      );
    }
  }
  return configs;
}

export const all_model_configs = new Map<string, ModelConfig>(load_model_configs().map(config => [config.id, config]));
