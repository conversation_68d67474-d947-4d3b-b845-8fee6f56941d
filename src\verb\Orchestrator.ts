import type { ChatPanelProvider } from '../exit/ChatPanelProvider.js';
import type { MessageToWebview } from '../exit/webviewMessageProtocol.js';
import type { ContextPackage, ExecutionResult } from '../object/dataModels.js';
import type { IComposableModule } from './moduleInterfaces.js';

/**
 * @description 主流程调度器，通过模块组合定义和执行核心业务逻辑。
 * This orchestrator embodies the "Natural Language Programming Paradigm" by chaining
 * modules together into a declarative processing pipeline.
 */
export class Orchestrator {
  private _context_capturer: IComposableModule;
  private _prompt_serializer: IComposableModule;
  private _llm_client: IComposableModule;
  private _command_parser: IComposableModule;
  private _command_executor: IComposableModule;
  private _chat_panel_provider: ChatPanelProvider;

  constructor(
    chat_panel_provider: ChatPanelProvider,
    context_capturer: IComposableModule,
    prompt_serializer: IComposableModule,
    llm_client: IComposableModule,
    command_parser: IComposableModule,
    command_executor: IComposableModule,
  ) {
    this._chat_panel_provider = chat_panel_provider;
    this._context_capturer = context_capturer;
    this._prompt_serializer = prompt_serializer;
    this._llm_client = llm_client;
    this._command_parser = command_parser;
    this._command_executor = command_executor;
  }

  /**
   * 处理用户请求的主流程
   * @param user_request 用户输入
   * @param model_key The key for the model to be used
   */
  async handle_request(user_request: string, model_key: string): Promise<void> {
    try {
      // Initialize the context package
      let context: ContextPackage = {
        user_request,
        full_text: '',
        selected_text: '',
        file_path: null,
        active_file_path: null,
        workspace_root: null,
        model_key,
        available_tools: [],
        prompt_string: '',
        tool_executions: [],
      };

      // Step 1: "Given a user request, capture the context."
      context = await this._context_capturer.execute(context);

      // Step 2: "Given a context, serialize a prompt."
      context = await this._prompt_serializer.execute(context);

      // Step 3: "Given a prompt, get a command from the LLM."
      context = await this._llm_client.execute(context);

      // Step 4: "Given a potential command, parse and validate it."
      context = await this._command_parser.execute(context);

      // Step 5: "Given a valid command, execute it."
      context = await this._command_executor.execute(context);

      // Final Step: Post the final result back to the webview.
      // The last execution result should contain the final output.
      const last_execution = context.tool_executions?.[context.tool_executions.length - 1];
      if (last_execution) {
        const summary = this.summarize_result(last_execution);
        void this._chat_panel_provider.post_message({
          type: 'addResponse',
          content: summary,
        } as MessageToWebview);
      } else {
        // This case might happen if the LLM fails to produce a command.
        // The LLM client should have already shown an error message.
        console.log('Orchestrator: Pipeline finished without a final execution result.');
      }
    } catch (error) {
      // Catch any unhandled exceptions from the pipeline.
      const error_message = error instanceof Error ? error.message : String(error);
      void this._chat_panel_provider.post_message({
        type: 'showError',
        error: `An unexpected error occurred in the orchestrator: ${error_message}`,
      } as MessageToWebview);
    }
  }

  private summarize_result(result: ExecutionResult): string {
    if (result.is_success) {
      let summary = `✅ Successfully executed '${result.command.tool_name}'.`;
      if (result.output) {
        const output_str = typeof result.output === 'string' ? result.output : JSON.stringify(result.output, null, 2);
        // Keep summary concise
        const truncated_output = output_str.length > 500 ? `${output_str.substring(0, 497)}...` : output_str;
        summary += `\n\n**Output:**\n\`\`\`\n${truncated_output}\n\`\`\``;
      }
      return summary;
    } else {
      return `❌ Failed to execute '${result.command.tool_name}'.\n\n**Error:**\n${result.error ?? 'Unknown error'}`;
    }
  }
}
