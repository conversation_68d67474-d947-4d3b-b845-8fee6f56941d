import * as vscode from 'vscode';
import { z } from 'zod';

import type { ChatPanelProvider } from '../../exit/ChatPanelProvider.js';
import type { CommandStorageModule } from '../../object/CommandStorageModule.js';
import type { ToolMap } from '../moduleInterfaces.js';

/**
 * @description Registers all the tools that the LLM can use.
 * @param command_storage - The command storage module.
 * @param chat_panel_provider - The chat panel provider.
 * @returns A map of all the registered tools.
 */
export function register_all_tools(
  command_storage: CommandStorageModule,
  chat_panel_provider: ChatPanelProvider,
): ToolMap {
  const all_tools: ToolMap = new Map();

  // Tool: Reply to user
  all_tools.set('reply_to_user', {
    description: 'Replies directly to the user in the chat window.',
    parameters_schema: z.object({
      message: z.string().describe('The message to display to the user.'),
    }),
    handler: (parameters: unknown) => {
      const { message } = parameters as { message: string };
      chat_panel_provider.post_message({ type: 'addResponse', content: message });
      return Promise.resolve(`Replied to user: "${message}"`);
    },
  });

  // Tool: Execute VS Code command
  all_tools.set('execute_vscode_command', {
    description: 'Executes a registered VS Code command by its ID.',
    parameters_schema: z.object({
      command_id: z.string().describe('The ID of the command to execute (e.g., "workbench.action.files.save").'),
    }),
    handler: async (parameters: unknown) => {
      const { command_id } = parameters as { command_id: string };
      await vscode.commands.executeCommand(command_id);
      return `Executed command: ${command_id}`;
    },
  });

  // Tool: Add command to whitelist
  all_tools.set('add_command_to_whitelist', {
    description: 'Adds a command to the whitelist so it can be executed.',
    parameters_schema: z.object({ command_id: z.string() }),
    handler: async (parameters: unknown) => {
      const { command_id } = parameters as { command_id: string };
      const current_whitelist = command_storage.get_whitelist_ids();
      if (!current_whitelist.includes(command_id)) {
        current_whitelist.push(command_id);
        await command_storage.save_whitelist(current_whitelist);
        return `Command '${command_id}' added to whitelist.`;
      }
      return `Command '${command_id}' is already in the whitelist.`;
    },
  });

  // Tool: Remove command from whitelist (with MCP core tool protection)
  all_tools.set('remove_command_from_whitelist', {
    description: 'Removes a command from the whitelist. MCP core tools cannot be removed.',
    parameters_schema: z.object({ command_id: z.string() }),
    handler: async (parameters: unknown) => {
      const { command_id } = parameters as { command_id: string };

      // 检查是否为MCP核心工具
      if (command_storage.is_mcp_core_tool(command_id)) {
        return `❌ Cannot remove MCP core tool '${command_id}' from whitelist. This tool is required for system functionality.`;
      }

      const success = await command_storage.safe_remove_from_whitelist(command_id);
      if (success) {
        return `✅ Command '${command_id}' removed from whitelist.`;
      } else {
        return `ℹ️ Command '${command_id}' was not in the whitelist.`;
      }
    },
  });

  // Tool: Get whitelisted commands
  all_tools.set('get_whitelisted_commands', {
    description: 'Gets the list of all commands currently in the whitelist.',
    parameters_schema: z.object({}),
    handler: () => {
      const all_commands = command_storage.get_all_commands();
      const whitelist_ids = new Set(command_storage.get_whitelist_ids());
      const whitelisted_commands = all_commands.filter(cmd => whitelist_ids.has(cmd.id));
      return Promise.resolve(whitelisted_commands);
    },
  });

  // Tool: Get all available commands (excluding whitelisted)
  all_tools.set('get_available_commands', {
    description: 'Gets all available VS Code commands that are not currently in the whitelist.',
    parameters_schema: z.object({}),
    handler: () => {
      const all_commands = command_storage.get_all_commands();
      const whitelist_ids = new Set(command_storage.get_whitelist_ids());
      const available_commands = all_commands.filter(cmd => !whitelist_ids.has(cmd.id));
      return Promise.resolve(available_commands);
    },
  });

  // Tool: Get MCP core tools information
  all_tools.set('get_mcp_core_tools', {
    description: 'Gets the list of MCP core tools that cannot be removed from the whitelist.',
    parameters_schema: z.object({}),
    handler: () => {
      const mcp_core_tools = command_storage.get_mcp_core_tools();
      return Promise.resolve({
        core_tools: mcp_core_tools,
        count: mcp_core_tools.length,
        description: 'These tools are essential for MCP functionality and cannot be removed from the whitelist.',
      });
    },
  });

  return all_tools;
}
